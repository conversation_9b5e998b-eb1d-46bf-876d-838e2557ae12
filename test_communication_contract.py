#!/usr/bin/env python3
"""
测试通信契约修复
验证 developer_node 是否正确使用统一通信契约生成代码
"""

def test_communication_context_generation():
    """测试通信上下文生成逻辑"""
    print("🔍 测试通信上下文生成")
    print("=" * 40)
    
    # 模拟报警器的状态
    state = {
        "unified_communication_contract": {
            "topic_map": {
                "光照采集端": {
                    "pub": ["/smart_home/光照采集端/data"],
                    "sub": []
                },
                "报警器": {
                    "pub": ["/smart_home/报警器/alarm"],
                    "sub": ["/smart_home/光照采集端/data"]
                }
            },
            "project_name": "smart_home"
        },
        "current_device_task": {
            "device_role": "报警器",
            "peripherals": []
        }
    }
    
    # 模拟 developer_node 中的逻辑
    unified_contract = state.get('unified_communication_contract', {})
    device_task = state['current_device_task']
    device_role = device_task.get('device_role', '') if device_task else ''
    communication_context = ""
    
    if unified_contract and device_role:
        topic_map = unified_contract.get('topic_map', {})
        device_topics = topic_map.get(device_role, {"pub": [], "sub": []})
        
        comm_instructions = []
        if device_topics["pub"]:
            pub_topics = ', '.join(device_topics["pub"])
            comm_instructions.append(f"PUBLISH data to: {pub_topics}")
        if device_topics["sub"]:
            sub_topics = ', '.join(device_topics["sub"])
            comm_instructions.append(f"SUBSCRIBE to: {sub_topics}")
        
        if comm_instructions:
            communication_context = f"""
<CommunicationContract>
**MANDATORY MQTT COMMUNICATION:**
{' and '.join(comm_instructions)}

**CRITICAL**: Use these exact topic strings in your MQTT publish/subscribe calls.
</CommunicationContract>
"""
            print(f"生成的通信上下文:")
            print(communication_context)
            
            # 验证结果
            assert "/smart_home/光照采集端/data" in communication_context, "应该包含订阅主题"
            assert "/smart_home/报警器/alarm" in communication_context, "应该包含发布主题"
            assert "SUBSCRIBE to" in communication_context, "应该包含订阅指令"
            assert "PUBLISH data to" in communication_context, "应该包含发布指令"
            
            print("✅ 报警器通信上下文生成正确")
            return communication_context
    
    print("❌ 未生成通信上下文")
    return None

def test_light_sensor_context():
    """测试光照传感器的通信上下文"""
    print("\n💡 测试光照传感器通信上下文")
    print("=" * 40)
    
    state = {
        "unified_communication_contract": {
            "topic_map": {
                "光照采集端": {
                    "pub": ["/smart_home/光照采集端/data"],
                    "sub": []
                },
                "报警器": {
                    "pub": ["/smart_home/报警器/alarm"],
                    "sub": ["/smart_home/光照采集端/data"]
                }
            },
            "project_name": "smart_home"
        },
        "current_device_task": {
            "device_role": "光照采集端",
            "peripherals": [{"name": "BH1750"}]
        }
    }
    
    # 模拟相同的逻辑
    unified_contract = state.get('unified_communication_contract', {})
    device_task = state['current_device_task']
    device_role = device_task.get('device_role', '') if device_task else ''
    
    if unified_contract and device_role:
        topic_map = unified_contract.get('topic_map', {})
        device_topics = topic_map.get(device_role, {"pub": [], "sub": []})
        
        comm_instructions = []
        if device_topics["pub"]:
            pub_topics = ', '.join(device_topics["pub"])
            comm_instructions.append(f"PUBLISH data to: {pub_topics}")
        if device_topics["sub"]:
            sub_topics = ', '.join(device_topics["sub"])
            comm_instructions.append(f"SUBSCRIBE to: {sub_topics}")
        
        if comm_instructions:
            communication_context = f"""
<CommunicationContract>
**MANDATORY MQTT COMMUNICATION:**
{' and '.join(comm_instructions)}

**CRITICAL**: Use these exact topic strings in your MQTT publish/subscribe calls.
</CommunicationContract>
"""
            print(f"生成的通信上下文:")
            print(communication_context)
            
            # 验证结果
            assert "/smart_home/光照采集端/data" in communication_context, "应该包含发布主题"
            assert "PUBLISH data to" in communication_context, "应该包含发布指令"
            assert "SUBSCRIBE to" not in communication_context, "不应该包含订阅指令"
            
            print("✅ 光照传感器通信上下文生成正确")
            return communication_context
    
    return None

def test_expected_code_behavior():
    """测试期望的代码行为"""
    print("\n🎯 测试期望的代码行为")
    print("=" * 40)
    
    # 基于通信契约，我们期望生成的代码应该包含：
    expected_alarm_code_patterns = [
        'localMqttClient.subscribe("/smart_home/光照采集端/data")',
        'if (topicStr == "/smart_home/光照采集端/data")',
        'localMqttClient.publish("/smart_home/报警器/alarm"'
    ]
    
    expected_sensor_code_patterns = [
        'localMqttClient.publish("/smart_home/光照采集端/data"'
    ]
    
    print("期望的报警器代码模式:")
    for pattern in expected_alarm_code_patterns:
        print(f"  - {pattern}")
    
    print("\n期望的光照传感器代码模式:")
    for pattern in expected_sensor_code_patterns:
        print(f"  - {pattern}")
    
    print("\n✅ 代码模式定义完成")
    return expected_alarm_code_patterns, expected_sensor_code_patterns

def main():
    """运行所有测试"""
    print("🚀 开始测试通信契约修复")
    print("=" * 60)
    
    try:
        # 测试报警器通信上下文
        alarm_context = test_communication_context_generation()
        
        # 测试光照传感器通信上下文
        sensor_context = test_light_sensor_context()
        
        # 测试期望的代码行为
        alarm_patterns, sensor_patterns = test_expected_code_behavior()
        
        if alarm_context and sensor_context:
            print("\n" + "=" * 60)
            print("🎉 通信契约修复测试通过！")
            print("\n修复效果预期:")
            print("✅ 报警器将订阅正确的主题: /smart_home/光照采集端/data")
            print("✅ 光照传感器将发布到正确的主题: /smart_home/光照采集端/data")
            print("✅ 两个设备使用相同的通信主题，建立通信链路")
            print("✅ 验证器将监听正确的主题")
            print("\n下一步:")
            print("1. 重新运行工作流生成新的固件代码")
            print("2. 检查生成的代码是否包含正确的主题")
            print("3. 验证设备间的通信是否正常")
            return 0
        else:
            print("\n❌ 通信上下文生成失败")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
