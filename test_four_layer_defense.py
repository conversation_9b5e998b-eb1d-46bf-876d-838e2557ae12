#!/usr/bin/env python3
"""
四层防御体系测试脚本
测试报警器设备是否能正确识别为远程数据源，并禁止本地传感器采集
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.langgraph_def.graph_builder import (
    generate_task_contract,
    _contains_forbidden_apis,
    _validate_task_contract
)

def test_alarm_device_contract():
    """测试报警器设备的任务契约生成"""
    print("=== 第一层测试：架构与契约层 ===")
    
    # 模拟报警器设备
    device_role = "报警器"
    peripherals = []  # 报警器没有传感器外设
    topic_map = {
        "报警器": {
            "pub": [],
            "sub": ["/smart_home/light_sensor/data"]  # 只订阅，不发布
        }
    }
    
    contract = generate_task_contract(device_role, peripherals, topic_map)
    
    print(f"设备角色: {device_role}")
    print(f"外设列表: {peripherals}")
    print(f"生成的契约:")
    print(f"  - 数据源: {contract['data_source']}")
    print(f"  - 核心逻辑: {contract['core_logic_summary']}")
    print(f"  - 允许的API: {contract['allowed_hardware_apis']}")
    print(f"  - 禁止的API: {contract['forbidden_hardware_apis']}")
    
    # 验证报警器被正确识别为远程数据源
    assert contract['data_source'] == 'remote', f"报警器应该是远程数据源，但得到: {contract['data_source']}"
    assert 'analogRead' in contract['forbidden_hardware_apis'], "报警器应该禁止analogRead"
    assert 'bh1750_read_lux' in contract['forbidden_hardware_apis'], "报警器应该禁止bh1750_read_lux"
    
    print("✅ 第一层测试通过：报警器正确识别为远程数据源")
    return contract

def test_sensor_device_contract():
    """测试传感器设备的任务契约生成"""
    print("\n=== 对比测试：传感器设备契约 ===")
    
    # 模拟光照传感器设备
    device_role = "光照采集端"
    peripherals = [{"name": "BH1750", "model": "BH1750FVI"}]
    topic_map = {
        "光照采集端": {
            "pub": ["/smart_home/light_sensor/data"],  # 发布数据
            "sub": []
        }
    }
    
    contract = generate_task_contract(device_role, peripherals, topic_map)
    
    print(f"设备角色: {device_role}")
    print(f"外设列表: {peripherals}")
    print(f"生成的契约:")
    print(f"  - 数据源: {contract['data_source']}")
    print(f"  - 允许的API: {contract['allowed_hardware_apis']}")
    print(f"  - 禁止的API: {contract['forbidden_hardware_apis']}")
    
    # 验证传感器设备被正确识别为本地数据源
    assert contract['data_source'] == 'local', f"传感器设备应该是本地数据源，但得到: {contract['data_source']}"
    assert 'bh1750_read_lux' in contract['allowed_hardware_apis'], "传感器设备应该允许bh1750_read_lux"
    
    print("✅ 传感器设备正确识别为本地数据源")

def test_forbidden_api_detection():
    """测试第四层：禁止API检测"""
    print("\n=== 第四层测试：确定性验证层 ===")
    
    # 测试包含禁止API的代码
    bad_code = """
    #include <Arduino.h>
    #include <WiFi.h>
    
    void setup() {
        Serial.begin(115200);
    }
    
    void loop() {
        int lightValue = analogRead(A0);  // 这是禁止的！
        float lux = bh1750_read_lux();    // 这也是禁止的！
        
        if (lightValue > 3000) {
            Serial.println("Light detected");
        }
        delay(1000);
    }
    """
    
    forbidden_apis = ['analogRead', 'bh1750_read_lux']
    violation = _contains_forbidden_apis(bad_code, forbidden_apis)
    
    print("测试代码包含禁止的API调用:")
    print("- analogRead(A0)")
    print("- bh1750_read_lux()")
    print(f"\n检测结果: {violation}")
    
    assert violation != "", "应该检测到禁止的API调用"
    assert "analogRead" in violation, "应该检测到analogRead违规"
    assert "bh1750_read_lux" in violation, "应该检测到bh1750_read_lux违规"
    
    print("✅ 第四层测试通过：成功检测到禁止的API调用")

def test_good_alarm_code():
    """测试符合契约的报警器代码"""
    print("\n=== 正向测试：符合契约的报警器代码 ===")
    
    good_code = """
    #include <Arduino.h>
    #include <WiFi.h>
    #include <PubSubClient.h>
    #include <ArduinoJson.h>
    
    WiFiClient wifiClient;
    PubSubClient localMqttClient(wifiClient);
    
    void mqtt_callback(char* topic, byte* payload, unsigned int length) {
        String payloadStr;
        for (unsigned int i = 0; i < length; i++) {
            payloadStr += (char)payload[i];
        }
        
        JsonDocument doc;
        deserializeJson(doc, payloadStr);
        
        if (doc.containsKey("lux")) {
            float lux = doc["lux"];
            if (lux < 50.0) {
                Serial.println("Low light detected - triggering alarm!");
                // 触发报警逻辑
            }
        }
    }
    
    void setup() {
        Serial.begin(115200);
        localMqttClient.setCallback(mqtt_callback);
        localMqttClient.subscribe("/smart_home/light_sensor/data");
    }
    
    void loop() {
        localMqttClient.loop();
        delay(100);
    }
    """
    
    # 测试任务契约验证
    task_contract = {
        'data_source': 'remote',
        'forbidden_hardware_apis': ['analogRead', 'bh1750_read_lux'],
        'allowed_hardware_apis': []
    }
    
    violation = _validate_task_contract(good_code, task_contract)
    
    print("测试符合契约的报警器代码:")
    print("- 使用MQTT订阅获取数据")
    print("- 解析JSON获取光照值")
    print("- 基于数据执行报警逻辑")
    print("- 没有调用任何传感器API")
    print(f"\n验证结果: {'通过' if not violation else violation}")
    
    assert violation == "", f"符合契约的代码不应该有违规，但得到: {violation}"
    
    print("✅ 正向测试通过：符合契约的代码验证成功")

def main():
    """运行所有测试"""
    print("🚀 开始测试四层防御体系")
    print("=" * 50)
    
    try:
        # 测试第一层：架构与契约层
        alarm_contract = test_alarm_device_contract()
        test_sensor_device_contract()
        
        # 测试第四层：确定性验证层
        test_forbidden_api_detection()
        test_good_alarm_code()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！四层防御体系工作正常")
        print("\n四层防御体系总结:")
        print("✅ 第一层：架构与契约层 - 正确识别设备类型和数据源")
        print("✅ 第二层：状态管理层 - 清理跨设备状态污染")
        print("✅ 第三层：指令遵从层 - 强化禁止性指令")
        print("✅ 第四层：确定性验证层 - 检测和阻止违规代码")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
