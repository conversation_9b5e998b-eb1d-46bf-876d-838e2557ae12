#!/usr/bin/env python3
"""
四层防御体系测试脚本
测试报警器设备是否能正确识别为远程数据源，并禁止本地传感器采集
"""

# 独立实现测试函数，避免依赖需要API密钥的模块

def generate_task_contract(device_role: str, peripherals: list, topic_map: dict) -> dict:
    """
    生成设备任务契约，明确定义数据源、允许/禁止的硬件API等
    """
    role_lower = device_role.lower()

    # 判断数据源类型
    data_source = "local"  # 默认为本地采集
    has_sensors = any(p.get('name', '').lower() in ['bh1750', 'ds1624', 'apds9960', 'dht22']
                     for p in peripherals)

    # 如果设备角色包含"报警"、"alarm"等关键词，且没有传感器外设，则为远程数据源
    alarm_keywords = ['报警', 'alarm', '警报', 'alert', '通知', 'notify']
    is_alarm_device = any(keyword in role_lower for keyword in alarm_keywords)

    if is_alarm_device and not has_sensors:
        data_source = "remote"

    # 如果设备只有订阅任务，没有发布任务，也可能是远程数据源
    device_comm = topic_map.get(device_role, {"pub": [], "sub": []})
    if device_comm["sub"] and not device_comm["pub"]:
        data_source = "remote"

    # 生成核心逻辑摘要
    if data_source == "remote":
        core_logic = f"Subscribe to MQTT topics, parse received data, and execute control logic based on the data values."
    else:
        sensor_names = [p.get('name', '') for p in peripherals if p.get('name')]
        if sensor_names:
            core_logic = f"Read data from local sensors ({', '.join(sensor_names)}), process the values, and publish to MQTT topics."
        else:
            core_logic = "Execute device-specific control logic and communicate via MQTT."

    # 定义允许和禁止的硬件API
    allowed_apis = []
    forbidden_apis = []

    if data_source == "local" and has_sensors:
        # 本地采集设备，允许调用传感器API
        for peripheral in peripherals:
            sensor_name = peripheral.get('name', '').lower()
            if sensor_name == 'bh1750':
                allowed_apis.extend(['bh1750_init', 'bh1750_read_lux'])
            elif sensor_name == 'ds1624':
                allowed_apis.extend(['ds1624_init', 'ds1624_read_temperature'])
            elif sensor_name == 'apds9960':
                allowed_apis.extend(['apds9960_init', 'apds9960_read_ambient_light'])
            elif sensor_name == 'dht22':
                allowed_apis.extend(['dht22_init', 'dht22_read'])

        # 如果有模拟传感器，允许analogRead
        analog_sensors = [p for p in peripherals if p.get('type') == 'analog']
        if analog_sensors:
            allowed_apis.append('analogRead')
    else:
        # 远程数据源设备，禁止所有传感器读取API
        forbidden_apis.extend([
            'analogRead', 'bh1750_read_lux', 'ds1624_read_temperature',
            'apds9960_read_ambient_light', 'dht22_read'
        ])

    return {
        "data_source": data_source,
        "core_logic_summary": core_logic,
        "allowed_hardware_apis": allowed_apis,
        "forbidden_hardware_apis": forbidden_apis
    }

def _contains_forbidden_apis(code: str, forbidden_apis: list) -> str:
    """
    检查生成的代码是否包含禁止的API调用（第四层防御）。
    返回违反原因的字符串，如果没有违反则返回空字符串。
    """
    if not code or not forbidden_apis:
        return ""

    violations = []
    code_lines = code.split('\n')

    for api in forbidden_apis:
        # 检查函数调用形式，如 analogRead(, bh1750_read_lux(
        api_call_pattern = f"{api}("

        for line_num, line in enumerate(code_lines, 1):
            # 跳过注释行
            stripped_line = line.strip()
            if stripped_line.startswith('//') or stripped_line.startswith('/*'):
                continue

            if api_call_pattern in line:
                violations.append(f"Line {line_num}: Forbidden API '{api}' detected in: {line.strip()}")

    if violations:
        return f"FORBIDDEN API VIOLATION: {'; '.join(violations)}"

    return ""

def _validate_task_contract(code: str, task_contract: dict) -> str:
    """
    基于任务契约验证代码合规性（第四层防御的核心函数）。
    结合正向契约检查和反向契约检查。
    """
    if not code or not task_contract:
        return ""

    violations = []

    # 1. 检查禁止的API（反向契约）
    forbidden_apis = task_contract.get('forbidden_hardware_apis', [])
    if forbidden_apis:
        forbidden_violation = _contains_forbidden_apis(code, forbidden_apis)
        if forbidden_violation:
            violations.append(forbidden_violation)

    # 2. 检查数据源约束
    data_source = task_contract.get('data_source', 'local')
    if data_source == 'remote':
        # 远程数据源设备不应该有任何传感器读取逻辑
        sensor_patterns = ['analogRead(', 'bh1750_', 'ds1624_', 'apds9960_', 'dht22_']
        for pattern in sensor_patterns:
            if pattern in code:
                violations.append(f"Remote data source device must not contain sensor reading pattern: {pattern}")

    # 3. 检查MQTT订阅逻辑（对于远程数据源设备）
    if data_source == 'remote':
        if 'localMqttClient.subscribe(' not in code and 'tuyaMqttClient.subscribe(' not in code:
            violations.append("Remote data source device must implement MQTT subscription logic")

    return "; ".join(violations) if violations else ""

def test_alarm_device_contract():
    """测试报警器设备的任务契约生成"""
    print("=== 第一层测试：架构与契约层 ===")
    
    # 模拟报警器设备
    device_role = "报警器"
    peripherals = []  # 报警器没有传感器外设
    topic_map = {
        "报警器": {
            "pub": [],
            "sub": ["/smart_home/light_sensor/data"]  # 只订阅，不发布
        }
    }
    
    contract = generate_task_contract(device_role, peripherals, topic_map)
    
    print(f"设备角色: {device_role}")
    print(f"外设列表: {peripherals}")
    print(f"生成的契约:")
    print(f"  - 数据源: {contract['data_source']}")
    print(f"  - 核心逻辑: {contract['core_logic_summary']}")
    print(f"  - 允许的API: {contract['allowed_hardware_apis']}")
    print(f"  - 禁止的API: {contract['forbidden_hardware_apis']}")
    
    # 验证报警器被正确识别为远程数据源
    assert contract['data_source'] == 'remote', f"报警器应该是远程数据源，但得到: {contract['data_source']}"
    assert 'analogRead' in contract['forbidden_hardware_apis'], "报警器应该禁止analogRead"
    assert 'bh1750_read_lux' in contract['forbidden_hardware_apis'], "报警器应该禁止bh1750_read_lux"
    
    print("✅ 第一层测试通过：报警器正确识别为远程数据源")
    return contract

def test_sensor_device_contract():
    """测试传感器设备的任务契约生成"""
    print("\n=== 对比测试：传感器设备契约 ===")
    
    # 模拟光照传感器设备
    device_role = "光照采集端"
    peripherals = [{"name": "BH1750", "model": "BH1750FVI"}]
    topic_map = {
        "光照采集端": {
            "pub": ["/smart_home/light_sensor/data"],  # 发布数据
            "sub": []
        }
    }
    
    contract = generate_task_contract(device_role, peripherals, topic_map)
    
    print(f"设备角色: {device_role}")
    print(f"外设列表: {peripherals}")
    print(f"生成的契约:")
    print(f"  - 数据源: {contract['data_source']}")
    print(f"  - 允许的API: {contract['allowed_hardware_apis']}")
    print(f"  - 禁止的API: {contract['forbidden_hardware_apis']}")
    
    # 验证传感器设备被正确识别为本地数据源
    assert contract['data_source'] == 'local', f"传感器设备应该是本地数据源，但得到: {contract['data_source']}"
    assert 'bh1750_read_lux' in contract['allowed_hardware_apis'], "传感器设备应该允许bh1750_read_lux"
    
    print("✅ 传感器设备正确识别为本地数据源")

def test_forbidden_api_detection():
    """测试第四层：禁止API检测"""
    print("\n=== 第四层测试：确定性验证层 ===")
    
    # 测试包含禁止API的代码
    bad_code = """
    #include <Arduino.h>
    #include <WiFi.h>
    
    void setup() {
        Serial.begin(115200);
    }
    
    void loop() {
        int lightValue = analogRead(A0);  // 这是禁止的！
        float lux = bh1750_read_lux();    // 这也是禁止的！
        
        if (lightValue > 3000) {
            Serial.println("Light detected");
        }
        delay(1000);
    }
    """
    
    forbidden_apis = ['analogRead', 'bh1750_read_lux']
    violation = _contains_forbidden_apis(bad_code, forbidden_apis)
    
    print("测试代码包含禁止的API调用:")
    print("- analogRead(A0)")
    print("- bh1750_read_lux()")
    print(f"\n检测结果: {violation}")
    
    assert violation != "", "应该检测到禁止的API调用"
    assert "analogRead" in violation, "应该检测到analogRead违规"
    assert "bh1750_read_lux" in violation, "应该检测到bh1750_read_lux违规"
    
    print("✅ 第四层测试通过：成功检测到禁止的API调用")

def test_good_alarm_code():
    """测试符合契约的报警器代码"""
    print("\n=== 正向测试：符合契约的报警器代码 ===")
    
    good_code = """
    #include <Arduino.h>
    #include <WiFi.h>
    #include <PubSubClient.h>
    #include <ArduinoJson.h>
    
    WiFiClient wifiClient;
    PubSubClient localMqttClient(wifiClient);
    
    void mqtt_callback(char* topic, byte* payload, unsigned int length) {
        String payloadStr;
        for (unsigned int i = 0; i < length; i++) {
            payloadStr += (char)payload[i];
        }
        
        JsonDocument doc;
        deserializeJson(doc, payloadStr);
        
        if (doc.containsKey("lux")) {
            float lux = doc["lux"];
            if (lux < 50.0) {
                Serial.println("Low light detected - triggering alarm!");
                // 触发报警逻辑
            }
        }
    }
    
    void setup() {
        Serial.begin(115200);
        localMqttClient.setCallback(mqtt_callback);
        localMqttClient.subscribe("/smart_home/light_sensor/data");
    }
    
    void loop() {
        localMqttClient.loop();
        delay(100);
    }
    """
    
    # 测试任务契约验证
    task_contract = {
        'data_source': 'remote',
        'forbidden_hardware_apis': ['analogRead', 'bh1750_read_lux'],
        'allowed_hardware_apis': []
    }
    
    violation = _validate_task_contract(good_code, task_contract)
    
    print("测试符合契约的报警器代码:")
    print("- 使用MQTT订阅获取数据")
    print("- 解析JSON获取光照值")
    print("- 基于数据执行报警逻辑")
    print("- 没有调用任何传感器API")
    print(f"\n验证结果: {'通过' if not violation else violation}")
    
    assert violation == "", f"符合契约的代码不应该有违规，但得到: {violation}"
    
    print("✅ 正向测试通过：符合契约的代码验证成功")

def main():
    """运行所有测试"""
    print("🚀 开始测试四层防御体系")
    print("=" * 50)
    
    try:
        # 测试第一层：架构与契约层
        alarm_contract = test_alarm_device_contract()
        test_sensor_device_contract()
        
        # 测试第四层：确定性验证层
        test_forbidden_api_detection()
        test_good_alarm_code()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！四层防御体系工作正常")
        print("\n四层防御体系总结:")
        print("✅ 第一层：架构与契约层 - 正确识别设备类型和数据源")
        print("✅ 第二层：状态管理层 - 清理跨设备状态污染")
        print("✅ 第三层：指令遵从层 - 强化禁止性指令")
        print("✅ 第四层：确定性验证层 - 检测和阻止违规代码")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
