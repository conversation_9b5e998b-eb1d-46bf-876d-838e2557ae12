#!/usr/bin/env python3
"""
测试 graph_builder 修复
验证通信契约验证函数是否正确工作
"""

def _validate_communication_contract(code: str, unified_contract: dict, device_role: str) -> str:
    """
    验证代码是否遵循统一通信契约（第四层防御）。
    检查是否使用了正确的 MQTT 主题。
    """
    if not code or not unified_contract or not device_role:
        return ""
    
    violations = []
    topic_map = unified_contract.get('topic_map', {})
    device_topics = topic_map.get(device_role, {"pub": [], "sub": []})
    
    # 检查是否使用了错误的占位符主题
    forbidden_topics = ['///data', '/sensor/value', '/sensor/light']
    for forbidden_topic in forbidden_topics:
        if f'"{forbidden_topic}"' in code:
            violations.append(f"Using forbidden placeholder topic: {forbidden_topic}")
    
    # 检查是否使用了正确的主题
    expected_topics = device_topics["pub"] + device_topics["sub"]
    if expected_topics:
        found_correct_topic = False
        for topic in expected_topics:
            if f'"{topic}"' in code:
                found_correct_topic = True
                break
        
        if not found_correct_topic:
            violations.append(f"Missing required communication topics: {expected_topics}")
    
    return "; ".join(violations) if violations else ""

def test_bad_alarm_code():
    """测试错误的报警器代码（使用占位符主题）"""
    print("🚫 测试错误的报警器代码")
    print("=" * 40)
    
    bad_code = '''
    void local_mqtt_callback(char* topic, byte* payload, unsigned int length) {
        String topicStr = String(topic);
        
        if (topicStr == "///data") {  // 错误的占位符主题
            // 处理数据
        }
    }
    
    void connectToLocalMqtt() {
        localMqttClient.subscribe("///data");  // 错误的占位符主题
    }
    '''
    
    unified_contract = {
        "topic_map": {
            "报警器": {
                "pub": ["/smart_home/报警器/alarm"],
                "sub": ["/smart_home/光照采集端/data"]
            }
        }
    }
    
    violation = _validate_communication_contract(bad_code, unified_contract, "报警器")
    
    print("错误代码:")
    print("- 使用了 ///data 占位符主题")
    print("- 没有使用正确的统一契约主题")
    print(f"\n检测结果: {violation}")
    
    assert violation != "", "应该检测到违规"
    assert "///data" in violation, "应该检测到占位符主题违规"
    assert "Missing required communication topics" in violation, "应该检测到缺少正确主题"
    
    print("✅ 错误代码检测成功")
    return violation

def test_good_alarm_code():
    """测试正确的报警器代码（使用统一契约主题）"""
    print("\n✅ 测试正确的报警器代码")
    print("=" * 40)
    
    good_code = '''
    void local_mqtt_callback(char* topic, byte* payload, unsigned int length) {
        String topicStr = String(topic);
        
        if (topicStr == "/smart_home/光照采集端/data") {  // 正确的统一契约主题
            // 处理光照数据
        }
    }
    
    void connectToLocalMqtt() {
        localMqttClient.subscribe("/smart_home/光照采集端/data");  // 正确的订阅主题
    }
    
    void publishAlarm() {
        localMqttClient.publish("/smart_home/报警器/alarm", payload);  // 正确的发布主题
    }
    '''
    
    unified_contract = {
        "topic_map": {
            "报警器": {
                "pub": ["/smart_home/报警器/alarm"],
                "sub": ["/smart_home/光照采集端/data"]
            }
        }
    }
    
    violation = _validate_communication_contract(good_code, unified_contract, "报警器")
    
    print("正确代码:")
    print("- 使用了统一契约中的订阅主题: /smart_home/光照采集端/data")
    print("- 使用了统一契约中的发布主题: /smart_home/报警器/alarm")
    print("- 没有使用任何占位符主题")
    print(f"\n检测结果: {'通过' if not violation else violation}")
    
    assert violation == "", f"正确的代码不应该有违规，但得到: {violation}"
    
    print("✅ 正确代码验证成功")
    return violation

def test_mixed_code():
    """测试混合代码（部分正确，部分错误）"""
    print("\n⚠️ 测试混合代码")
    print("=" * 40)
    
    mixed_code = '''
    void local_mqtt_callback(char* topic, byte* payload, unsigned int length) {
        String topicStr = String(topic);
        
        if (topicStr == "/smart_home/光照采集端/data") {  // 正确的主题
            // 处理数据
        }
        
        if (topicStr == "///data") {  // 错误的占位符主题
            // 旧的处理逻辑
        }
    }
    '''
    
    unified_contract = {
        "topic_map": {
            "报警器": {
                "pub": ["/smart_home/报警器/alarm"],
                "sub": ["/smart_home/光照采集端/data"]
            }
        }
    }
    
    violation = _validate_communication_contract(mixed_code, unified_contract, "报警器")
    
    print("混合代码:")
    print("- 包含正确的统一契约主题")
    print("- 但也包含错误的占位符主题")
    print(f"\n检测结果: {violation}")
    
    assert violation != "", "应该检测到占位符主题违规"
    assert "///data" in violation, "应该检测到占位符主题"
    
    print("✅ 混合代码检测成功")
    return violation

def main():
    """运行所有测试"""
    print("🚀 开始测试 graph_builder 通信契约修复")
    print("=" * 60)
    
    try:
        # 测试错误代码检测
        bad_violation = test_bad_alarm_code()
        
        # 测试正确代码验证
        good_violation = test_good_alarm_code()
        
        # 测试混合代码
        mixed_violation = test_mixed_code()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！通信契约验证功能正常")
        print("\n修复总结:")
        print("✅ 能够检测占位符主题违规 (///data, /sensor/value)")
        print("✅ 能够检测缺少正确的统一契约主题")
        print("✅ 能够验证正确使用统一契约主题的代码")
        print("✅ 第四层防御系统已增强通信契约检查")
        
        print("\n预期效果:")
        print("- 生成错误主题的代码将被自动拒绝")
        print("- 系统将强制重新生成使用正确主题的代码")
        print("- 报警器将能够正确订阅光照传感器的数据")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
