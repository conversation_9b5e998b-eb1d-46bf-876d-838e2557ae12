#!/usr/bin/env python3
"""
测试工作流状态 - 检查实际运行的工作流是否使用了我们的修复
这个测试会检查实际生成的文件，而不是理论上的函数
"""

import os
import json
import re

def analyze_generated_code():
    """分析实际生成的代码"""
    print("🔍 分析实际生成的代码")
    print("=" * 50)
    
    # 查找最新的工作空间
    workspace_base = "app/temp_workspaces"
    if not os.path.exists(workspace_base):
        print("❌ 工作空间目录不存在")
        return False
    
    # 找到最新的工作空间
    workspaces = [d for d in os.listdir(workspace_base) if d.startswith('wf-')]
    if not workspaces:
        print("❌ 没有找到工作空间")
        return False
    
    latest_workspace = sorted(workspaces)[-1]
    workspace_path = os.path.join(workspace_base, latest_workspace)
    print(f"📁 检查工作空间: {latest_workspace}")
    
    # 分析报警器代码
    alarm_devices = [d for d in os.listdir(workspace_path) if d.startswith('zygo_dev_') and d != 'zygo_dev_f29d']
    if not alarm_devices:
        print("❌ 没有找到报警器设备")
        return False
    
    alarm_device = alarm_devices[0]
    alarm_code_path = os.path.join(workspace_path, alarm_device, 'src', 'app_main.ino')
    
    if not os.path.exists(alarm_code_path):
        print(f"❌ 报警器代码文件不存在: {alarm_code_path}")
        return False
    
    with open(alarm_code_path, 'r', encoding='utf-8') as f:
        alarm_code = f.read()
    
    print(f"📄 分析报警器代码: {alarm_device}")
    
    # 检查主题使用情况
    topics_found = []
    if '///data' in alarm_code:
        topics_found.append("❌ 使用了错误的占位符主题: ///data")
    if '/sensor/value' in alarm_code:
        topics_found.append("❌ 使用了错误的推导主题: /sensor/value")
    if '/sensor/light' in alarm_code:
        topics_found.append("⚠️ 使用了传统推导主题: /sensor/light")
    
    # 检查是否使用了统一契约主题
    unified_topics = re.findall(r'"/[^"]*?/[^"]*?/data"', alarm_code)
    if unified_topics:
        for topic in unified_topics:
            topics_found.append(f"✅ 使用了统一契约主题: {topic}")
    
    # 检查键名使用情况
    keys_found = []
    if '"illumination"' in alarm_code:
        keys_found.append("❌ 使用了错误的推导键名: illumination")
    if '"illuminance_lux"' in alarm_code:
        keys_found.append("✅ 使用了DP契约键名: illuminance_lux")
    if '"lux"' in alarm_code:
        keys_found.append("⚠️ 使用了简化键名: lux")
    
    print("\n🎯 主题使用情况:")
    for topic in topics_found:
        print(f"  {topic}")
    
    print("\n🔑 键名使用情况:")
    for key in keys_found:
        print(f"  {key}")
    
    # 分析验证脚本
    verify_script_path = os.path.join(workspace_path, alarm_device, 'run_verification.py')
    if os.path.exists(verify_script_path):
        with open(verify_script_path, 'r', encoding='utf-8') as f:
            verify_code = f.read()
        
        print("\n📋 验证脚本期望:")
        topic_match = re.search(r'TOPIC_TO_VERIFY = "([^"]+)"', verify_code)
        if topic_match:
            expected_topic = topic_match.group(1)
            print(f"  期望主题: {expected_topic}")
        
        key_match = re.search(r'"([^"]+):"', verify_code)
        if key_match:
            expected_key = key_match.group(1)
            print(f"  期望键名: {expected_key}")
    
    # 判断修复是否生效
    has_correct_topics = any("✅" in topic for topic in topics_found)
    has_correct_keys = any("✅" in key for key in keys_found)
    has_wrong_topics = any("❌" in topic for topic in topics_found)
    
    print(f"\n📊 修复状态评估:")
    if has_correct_topics and has_correct_keys and not has_wrong_topics:
        print("✅ 修复完全生效 - 使用了正确的统一契约")
        return True
    elif has_correct_topics or has_correct_keys:
        print("⚠️ 修复部分生效 - 仍有问题需要解决")
        return False
    else:
        print("❌ 修复未生效 - 仍在使用旧的错误模式")
        return False

def check_graph_builder_modifications():
    """检查 graph_builder.py 是否包含我们的修改"""
    print("\n🔧 检查 graph_builder.py 修改")
    print("=" * 50)
    
    graph_builder_path = "app/langgraph_def/graph_builder.py"
    if not os.path.exists(graph_builder_path):
        print("❌ graph_builder.py 文件不存在")
        return False
    
    with open(graph_builder_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    modifications_check = [
        ("unified_communication_contract", "统一通信契约"),
        ("_validate_communication_contract", "通信契约验证函数"),
        ("CommunicationContract", "通信契约上下文"),
        ("MANDATORY MQTT COMMUNICATION", "强制MQTT通信指令"),
        ("Communication Contract:", "通信契约违规检查")
    ]
    
    print("🔍 检查关键修改:")
    all_present = True
    for pattern, description in modifications_check:
        if pattern in content:
            print(f"  ✅ {description}: 已存在")
        else:
            print(f"  ❌ {description}: 缺失")
            all_present = False
    
    return all_present

def suggest_next_steps():
    """建议下一步行动"""
    print("\n🚀 建议的下一步行动:")
    print("=" * 50)
    
    print("1. 🔄 重新启动工作流")
    print("   - 当前工作流实例使用的是旧版本代码")
    print("   - 需要重新运行以使用修复后的 graph_builder.py")
    
    print("\n2. 🎯 验证修复效果")
    print("   - 检查新生成的代码是否使用统一契约主题")
    print("   - 确认验证脚本期望正确的主题和键名")
    
    print("\n3. 📊 监控关键指标")
    print("   - 报警器订阅主题: 应该是 /项目名/光照采集端/data")
    print("   - 光照传感器发布主题: 应该与报警器订阅主题相同")
    print("   - JSON键名: 应该使用DP契约中的实际code")
    
    print("\n4. 🔍 调试信息")
    print("   - 查看控制台输出中的 'Generated communication context' 信息")
    print("   - 确认 'Contract violations detected' 是否正确触发")

def main():
    """运行完整的工作流状态测试"""
    print("🚀 开始工作流状态分析")
    print("=" * 60)
    
    try:
        # 检查代码修改
        modifications_ok = check_graph_builder_modifications()
        
        # 分析生成的代码
        code_analysis_ok = analyze_generated_code()
        
        # 建议下一步
        suggest_next_steps()
        
        print("\n" + "=" * 60)
        if modifications_ok and code_analysis_ok:
            print("🎉 修复已完全生效！")
            return 0
        elif modifications_ok:
            print("⚠️ 修复代码已就位，但需要重新运行工作流")
            return 1
        else:
            print("❌ 修复代码有问题，需要检查")
            return 2
            
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 3

if __name__ == "__main__":
    exit(main())
